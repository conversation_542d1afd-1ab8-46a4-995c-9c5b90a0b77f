<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateNotificationsTable extends Migration
{
    public function up()
    {
        // Create notifications table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'User who receives the notification'
            ],
            'actor_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'User who performed the action'
            ],
            'type' => [
                'type' => 'ENUM',
                'constraint' => ['like_post', 'like_comment', 'comment_post', 'reply_comment', 'follow_user'],
                'comment' => 'Type of notification'
            ],
            'target_type' => [
                'type' => 'ENUM',
                'constraint' => ['post', 'comment', 'user'],
                'comment' => 'Type of target object'
            ],
            'target_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'ID of the target object'
            ],
            'message' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'comment' => 'Notification message'
            ],
            'is_read' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => 'Whether notification has been read'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('actor_id');
        $this->forge->addKey(['type', 'target_type', 'target_id']);
        $this->forge->addKey('is_read');
        $this->forge->addKey('created_at');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('actor_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('notifications');
    }

    public function down()
    {
        $this->forge->dropTable('notifications');
    }
}
