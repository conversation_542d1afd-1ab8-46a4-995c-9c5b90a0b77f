<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $useSoftDeletes = true;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $allowedFields = [
        'full_name',
        'email',
        'password',
        'is_verified',
        'verification_token',
        'google_id',
        'profile',
        'sub_start',
        'sub_end',
        'payment_id',
        'client_id',
        'access_token',
        'broker',
        'connected_on',
        'refer_code',
        'refer_by',
        'angel_client_id',
        'angel_api_key',
        'angel_access_token',
        'angel_refresh_token',
        'angel_feed_token',
        'angel_connected_on',
        'angel_totp_key',
        'angel_mpin'
    ];

}