<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PostModel;
use App\Models\CommentModel;
use App\Models\LikeModel;
use App\Models\FollowModel;
use App\Models\UserModel;

class CommunityController extends BaseController
{
    protected $postModel;
    protected $commentModel;
    protected $likeModel;
    protected $followModel;
    protected $userModel;

    public function __construct()
    {
        $this->postModel = new PostModel();
        $this->commentModel = new CommentModel();
        $this->likeModel = new LikeModel();
        $this->followModel = new FollowModel();
        $this->userModel = new UserModel();
    }

    /**
     * Display community feed
     */
    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $data['title'] = 'Community Feed';
        $data['active'] = 'community';
        $data['userDetails'] = $this->userModel->find($userId);
        $data['customScript'] = 'community';
        $data['main_content'] = 'pages/community';

        return view('includes/template', $data);
    }

    /**
     * Get posts for feed (AJAX)
     */
    public function getPosts()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $page = $this->request->getGet('page') ?? 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;
        
        $filters = [
            'post_type' => $this->request->getGet('post_type') ?? 'all',
            'asset_class' => $this->request->getGet('asset_class') ?? 'all',
            'sort' => $this->request->getGet('sort') ?? 'latest',
            'following_only' => $this->request->getGet('following_only') ?? false
        ];

        if ($filters['following_only']) {
            $posts = $this->followModel->getFollowingPosts($userId, $limit, $offset);
        } else {
            $posts = $this->postModel->getPostsWithUserInfo($limit, $offset, $filters);
        }

        // Get like status for current user
        $likeItems = [];
        foreach ($posts as $post) {
            $likeItems[] = ['type' => 'post', 'id' => $post['id']];
        }
        $likedItems = $this->likeModel->getLikeStatusForItems($userId, $likeItems);

        // Get follow status for post authors
        $authorIds = array_column($posts, 'user_id');
        $followingUsers = $this->followModel->getFollowStatusForUsers($userId, $authorIds);

        // Add status to posts
        foreach ($posts as &$post) {
            $post['is_liked'] = isset($likedItems['post_' . $post['id']]);
            $post['is_following'] = isset($followingUsers[$post['user_id']]);
            $post['is_own_post'] = $post['user_id'] == $userId;
            
            // Format time ago
            $post['time_ago'] = $this->timeAgo($post['created_at']);
            
            // Parse tags
            $post['parsed_tags'] = $this->parseTags($post['tags']);
        }

        return $this->response->setJSON([
            'success' => true,
            'posts' => $posts,
            'has_more' => count($posts) == $limit
        ]);
    }

    /**
     * Create new post
     */
    public function createPost()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $validation = \Config\Services::validation();
        $validation->setRules([
            'title' => 'required|max_length[255]',
            'content' => 'required',
            'post_type' => 'required|in_list[setup,pnl,analysis,educational,general]',
            'asset_class' => 'required|in_list[equity,options,futures,crypto,forex,all]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        $data = [
            'user_id' => $userId,
            'title' => $this->request->getPost('title'),
            'content' => $this->request->getPost('content'),
            'post_type' => $this->request->getPost('post_type'),
            'asset_class' => $this->request->getPost('asset_class'),
            'tags' => json_encode($this->extractTags($this->request->getPost('content')))
        ];

        // Handle image upload if present
        $image = $this->request->getFile('image');
        if ($image && $image->isValid() && !$image->hasMoved()) {
            $newName = $image->getRandomName();
            $image->move(WRITEPATH . 'uploads/community', $newName);
            $data['image_url'] = base_url('writable/uploads/community/' . $newName);
        }

        if ($this->postModel->insert($data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Post created successfully',
                'post_id' => $this->postModel->getInsertID()
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to create post'
        ]);
    }

    /**
     * Toggle like on post or comment
     */
    public function toggleLike()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $likeableType = $this->request->getPost('type'); // 'post' or 'comment'
        $likeableId = $this->request->getPost('id');

        if (!in_array($likeableType, ['post', 'comment']) || !$likeableId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid parameters'
            ]);
        }

        $result = $this->likeModel->toggleLike($userId, $likeableType, $likeableId);
        
        if ($result['action'] === 'error') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to toggle like'
            ]);
        }

        // Get updated like count
        $likeCount = $this->likeModel->getLikeCount($likeableType, $likeableId);

        return $this->response->setJSON([
            'success' => true,
            'action' => $result['action'],
            'liked' => $result['liked'],
            'like_count' => $likeCount
        ]);
    }

    /**
     * Add comment to post
     */
    public function addComment()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $validation = \Config\Services::validation();
        $validation->setRules([
            'post_id' => 'required|integer',
            'content' => 'required|max_length[1000]',
            'parent_id' => 'permit_empty|integer'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        $data = [
            'post_id' => $this->request->getPost('post_id'),
            'user_id' => $userId,
            'content' => $this->request->getPost('content'),
            'parent_id' => $this->request->getPost('parent_id') ?: null
        ];

        if ($this->commentModel->insert($data)) {
            $commentId = $this->commentModel->getInsertID();
            
            // Get the comment with user info
            $comment = $this->commentModel
                ->select('community_comments.*, users.full_name, users.profile, users.badge')
                ->join('users', 'users.id = community_comments.user_id')
                ->find($commentId);
            
            $comment['time_ago'] = $this->timeAgo($comment['created_at']);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Comment added successfully',
                'comment' => $comment
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to add comment'
        ]);
    }

    /**
     * Get comments for a post
     */
    public function getComments($postId)
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $comments = $this->commentModel->getPostComments($postId);
        
        // Add time ago and like status
        foreach ($comments as &$comment) {
            $comment['time_ago'] = $this->timeAgo($comment['created_at']);
            $comment['is_liked'] = $this->likeModel->hasUserLiked($userId, 'comment', $comment['id']);
            
            // Process replies
            if (isset($comment['replies'])) {
                foreach ($comment['replies'] as &$reply) {
                    $reply['time_ago'] = $this->timeAgo($reply['created_at']);
                    $reply['is_liked'] = $this->likeModel->hasUserLiked($userId, 'comment', $reply['id']);
                }
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'comments' => $comments
        ]);
    }

    /**
     * Toggle follow user
     */
    public function toggleFollow()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $followingId = $this->request->getPost('user_id');

        if (!$followingId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User ID is required'
            ]);
        }

        $result = $this->followModel->toggleFollow($userId, $followingId);

        if ($result['action'] === 'error') {
            return $this->response->setJSON([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to toggle follow'
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'action' => $result['action'],
            'following' => $result['following']
        ]);
    }

    /**
     * Get user's own posts
     */
    public function getMyPosts()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $page = $this->request->getGet('page') ?? 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;

        $posts = $this->postModel->getUserPosts($userId, $limit, $offset);

        // Add user info and format
        foreach ($posts as &$post) {
            $user = $this->userModel->find($userId);
            $post['full_name'] = $user['full_name'];
            $post['profile'] = $user['profile'];
            $post['badge'] = $user['badge'];
            $post['time_ago'] = $this->timeAgo($post['created_at']);
            $post['is_own_post'] = true;
            $post['parsed_tags'] = $this->parseTags($post['tags']);
        }

        return $this->response->setJSON([
            'success' => true,
            'posts' => $posts,
            'has_more' => count($posts) == $limit
        ]);
    }

    /**
     * Delete user's own post
     */
    public function deletePost($postId)
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $post = $this->postModel->find($postId);

        if (!$post) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Post not found'
            ]);
        }

        if ($post['user_id'] != $userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You can only delete your own posts'
            ]);
        }

        if ($this->postModel->delete($postId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Post deleted successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to delete post'
        ]);
    }

    /**
     * Search posts
     */
    public function searchPosts()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $query = $this->request->getGet('q');

        if (empty($query) || strlen($query) < 3) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Search query must be at least 3 characters'
            ]);
        }

        $page = $this->request->getGet('page') ?? 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;

        $posts = $this->postModel->searchPosts($query, $limit, $offset);

        // Get like and follow status
        $likeItems = [];
        $authorIds = [];
        foreach ($posts as $post) {
            $likeItems[] = ['type' => 'post', 'id' => $post['id']];
            $authorIds[] = $post['user_id'];
        }

        $likedItems = $this->likeModel->getLikeStatusForItems($userId, $likeItems);
        $followingUsers = $this->followModel->getFollowStatusForUsers($userId, $authorIds);

        foreach ($posts as &$post) {
            $post['is_liked'] = isset($likedItems['post_' . $post['id']]);
            $post['is_following'] = isset($followingUsers[$post['user_id']]);
            $post['is_own_post'] = $post['user_id'] == $userId;
            $post['time_ago'] = $this->timeAgo($post['created_at']);
            $post['parsed_tags'] = $this->parseTags($post['tags']);
        }

        return $this->response->setJSON([
            'success' => true,
            'posts' => $posts,
            'has_more' => count($posts) == $limit
        ]);
    }

    /**
     * Helper function to calculate time ago
     */
    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . 'm ago';
        if ($time < 86400) return floor($time/3600) . 'h ago';
        if ($time < 2592000) return floor($time/86400) . 'd ago';
        if ($time < 31536000) return floor($time/2592000) . 'mo ago';
        return floor($time/31536000) . 'y ago';
    }

    /**
     * Extract hashtags and mentions from content
     */
    private function extractTags($content)
    {
        $tags = [];

        // Extract hashtags
        preg_match_all('/#([a-zA-Z0-9_]+)/', $content, $hashtags);
        if (!empty($hashtags[1])) {
            foreach ($hashtags[1] as $tag) {
                $tags[] = ['type' => 'hashtag', 'value' => $tag];
            }
        }

        // Extract mentions
        preg_match_all('/@([a-zA-Z0-9_]+)/', $content, $mentions);
        if (!empty($mentions[1])) {
            foreach ($mentions[1] as $mention) {
                $tags[] = ['type' => 'mention', 'value' => $mention];
            }
        }

        // Extract stock symbols
        preg_match_all('/\$([a-zA-Z0-9_]+)/', $content, $stocks);
        if (!empty($stocks[1])) {
            foreach ($stocks[1] as $stock) {
                $tags[] = ['type' => 'stock', 'value' => $stock];
            }
        }

        return $tags;
    }

    /**
     * Parse tags for display
     */
    private function parseTags($tagsJson)
    {
        if (empty($tagsJson)) {
            return [];
        }

        $tags = json_decode($tagsJson, true);
        return is_array($tags) ? $tags : [];
    }
}
