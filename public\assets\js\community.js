// Community Feed JavaScript
class CommunityFeed {
    constructor() {
        this.currentPage = 1;
        this.currentTab = 'all';
        this.currentFilters = {
            post_type: 'all',
            asset_class: 'all',
            sort: 'latest'
        };
        this.isLoading = false;
        this.hasMore = true;
        this.initialized = false;
        this.isSubmitting = false;
        this.lastSubmissionTime = 0;

        this.init();
    }

    init() {
        if (this.initialized) {
            return;
        }
        this.bindEvents();
        this.loadPosts(true);
        this.initialized = true;
    }

    bindEvents() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Filter changes
        document.getElementById('assetFilter').addEventListener('change', (e) => {
            this.currentFilters.asset_class = e.target.value;
            this.loadPosts(true);
        });

        document.getElementById('sortFilter').addEventListener('change', (e) => {
            this.currentFilters.sort = e.target.value;
            this.loadPosts(true);
        });

        // Create post modal
        document.getElementById('createPostBtn').addEventListener('click', () => {
            this.openCreatePostModal();
        });

        document.getElementById('closeModalBtn').addEventListener('click', () => {
            this.closeCreatePostModal();
        });

        document.getElementById('cancelPostBtn').addEventListener('click', () => {
            this.closeCreatePostModal();
        });

        // Post type selection
        document.querySelectorAll('.post-type-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.selectPostType(e.target.dataset.type);
            });
        });

        // Image upload
        document.getElementById('postImage').addEventListener('change', (e) => {
            this.handleImageUpload(e);
        });

        document.getElementById('removeImage').addEventListener('click', () => {
            this.removeImage();
        });

        // Form submission
        document.getElementById('createPostForm').addEventListener('submit', (e) => {
            e.preventDefault();

            // Prevent rapid double submissions
            const now = Date.now();
            if (this.lastSubmissionTime && (now - this.lastSubmissionTime) < 2000) {
                console.log('Preventing rapid double submission');
                return;
            }
            this.lastSubmissionTime = now;

            this.submitPost();
        });

        // Load more posts
        document.getElementById('loadMoreBtn').addEventListener('click', () => {
            this.loadPosts(false);
        });

        // Close modal on outside click
        document.getElementById('createPostModal').addEventListener('click', (e) => {
            if (e.target.id === 'createPostModal') {
                this.closeCreatePostModal();
            }
        });
    }

    switchTab(tab) {
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('tab-active');
            btn.classList.add('text-gray-400');
            btn.classList.remove('text-white');
        });

        const activeBtn = document.querySelector(`[data-tab="${tab}"]`);
        activeBtn.classList.add('tab-active');
        activeBtn.classList.remove('text-gray-400');
        activeBtn.classList.add('text-white');

        this.currentTab = tab;

        // Set filters based on tab
        if (tab === 'my-posts') {
            this.currentFilters.user_only = true;
        } else {
            this.currentFilters.user_only = false;
            if (tab !== 'all') {
                this.currentFilters.post_type = tab;
            } else {
                this.currentFilters.post_type = 'all';
            }
        }

        this.loadPosts(true);
    }

    async loadPosts(reset = false) {
        if (this.isLoading) return;

        this.isLoading = true;

        if (reset) {
            this.currentPage = 1;
            this.hasMore = true;
        }

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                ...this.currentFilters
            });

            let endpoint = '/community/getPosts';
            if (this.currentTab === 'my-posts') {
                endpoint = '/community/getMyPosts';
            }

            const response = await fetch(`${endpoint}?${params}`);
            const data = await response.json();

            if (data.success) {
                if (reset) {
                    document.getElementById('postsContainer').innerHTML = '';
                }

                this.renderPosts(data.posts);
                this.hasMore = data.has_more;
                this.currentPage++;

                // Show/hide load more button
                const loadMoreBtn = document.getElementById('loadMoreBtn');
                if (this.hasMore) {
                    loadMoreBtn.style.display = 'block';
                } else {
                    loadMoreBtn.style.display = 'none';
                }
            } else {
                this.showError(data.message || 'Failed to load posts');
            }
        } catch (error) {
            console.error('Error loading posts:', error);
            this.showError('Failed to load posts');
        } finally {
            this.isLoading = false;
        }
    }

    // Modal functions
    openCreatePostModal() {
        document.getElementById('createPostModal').classList.add('active');
    }

    closeCreatePostModal() {
        document.getElementById('createPostModal').classList.remove('active');
        this.resetCreatePostForm();
    }

    resetCreatePostForm() {
        document.getElementById('createPostForm').reset();
        document.getElementById('postType').value = 'general';
        document.querySelectorAll('.post-type-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector('[data-type="general"]').classList.add('active');
        this.removeImage();
    }

    selectPostType(type) {
        document.querySelectorAll('.post-type-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-type="${type}"]`).classList.add('active');
        document.getElementById('postType').value = type;
    }

    handleImageUpload(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                document.getElementById('previewImg').src = e.target.result;
                document.getElementById('imagePreview').classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
    }

    removeImage() {
        document.getElementById('postImage').value = '';
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('previewImg').src = '';
    }

    async submitPost() {
        // Prevent multiple submissions
        if (this.isSubmitting) {
            return;
        }

        this.isSubmitting = true;
        const form = document.getElementById('createPostForm');
        const formData = new FormData(form);

        // Add unique submission token to prevent duplicates
        const submissionToken = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        formData.append('submission_token', submissionToken);

        const submitBtn = document.getElementById('submitPostBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Posting...';

        try {
            const response = await fetch('/community/createPost', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.closeCreatePostModal();
                this.showSuccess('Post created successfully!');

                // Instead of reloading all posts, just refresh the current view
                // Only reload if we're on the "All" or "My Posts" tab
                if (this.currentTab === 'all' || this.currentTab === 'my-posts') {
                    this.loadPosts(true);
                }
            } else {
                this.showError(data.message || 'Failed to create post');
            }
        } catch (error) {
            console.error('Error creating post:', error);
            this.showError('Failed to create post');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Post';
            this.isSubmitting = false;
        }
    }

    renderPosts(posts) {
        const container = document.getElementById('postsContainer');

        if (posts.length === 0 && this.currentPage === 1) {
            container.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-comments text-4xl text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-400 mb-2">No posts found</h3>
                    <p class="text-gray-500">Be the first to share something with the community!</p>
                </div>
            `;
            return;
        }

        posts.forEach(post => {
            // Check if post already exists to prevent duplicates
            const existingPost = container.querySelector(`[data-post-id="${post.id}"]`);
            if (!existingPost) {
                const postElement = this.createPostElement(post);
                container.appendChild(postElement);
            }
        });
    }

    createPostElement(post) {
        const postDiv = document.createElement('div');
        postDiv.className = 'post-card glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg';
        postDiv.dataset.postId = post.id;

        const badgeClass = this.getBadgeClass(post.badge);
        const postTypeColor = this.getPostTypeColor(post.post_type);

        postDiv.innerHTML = `
            <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                <div class="flex items-center gap-3">
                    <img src="${post.profile || 'https://randomuser.me/api/portraits/men/32.jpg'}" alt="User" class="w-10 h-10 rounded-full border-2 border-purple-400">
                    <div>
                        <div class="font-bold text-white">${post.full_name}</div>
                        <div class="text-xs text-gray-400">
                            ${post.time_ago} · <span class="${postTypeColor}">${this.formatPostType(post.post_type)}</span>
                        </div>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-2 sm:justify-end">
                    ${!post.is_own_post ? `
                        <button class="follow-btn text-xs text-white px-3 py-1 rounded-full ${post.is_following ? 'following bg-gray-700 border border-teal-400' : 'bg-blue-600 hover:bg-blue-700'} transition" onclick="communityFeed.toggleFollow(${post.user_id}, this)">
                            ${post.is_following ? 'Following' : 'Follow'}
                        </button>
                    ` : ''}
                    ${post.badge !== 'none' ? `<span class="${badgeClass} text-xs text-white px-2 py-1 rounded-full">${this.formatBadge(post.badge)}</span>` : ''}
                    ${post.is_own_post ? `
                        <button class="text-gray-400 hover:text-red-400 transition-colors duration-200 p-2 rounded-full hover:bg-red-900/20"
                                onclick="communityFeed.deletePost(${post.id})"
                                title="Delete post">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    ` : ''}
                </div>
            </div>

            <h3 class="text-lg font-bold text-white mb-3">${post.title}</h3>
            <p class="text-gray-300 mb-4">${this.formatContent(post.content)}</p>

            ${post.image_url ? `
                <div class="mb-4 flex justify-start">
                    <img src="${post.image_url}" alt="Post image" class="w-full max-w-xl rounded-lg object-cover"
                         onerror="console.error('Failed to load image:', '${post.image_url}'); this.style.display='none';"
                         onload="console.log('Image loaded successfully:', '${post.image_url}');">
                </div>
            ` : ''}

            ${post.parsed_tags && post.parsed_tags.length > 0 ? `
                <div class="flex flex-wrap gap-2 mb-4">
                    ${post.parsed_tags.map(tag => `<span class="${this.getTagClass(tag.type)}">${this.formatTag(tag)}</span>`).join('')}
                </div>
            ` : ''}

            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-3 border-t border-gray-800 post-actions">
                <div class="flex flex-wrap gap-4">
                    <button class="reaction-btn flex items-center text-gray-400 hover:text-teal-400 ${post.is_liked ? 'text-teal-400' : ''}" onclick="communityFeed.toggleLike('post', ${post.id}, this)">
                        <i class="${post.is_liked ? 'fas' : 'far'} fa-thumbs-up mr-1"></i>
                        <span class="like-count">${post.likes_count}</span>
                    </button>
                    <button class="reaction-btn flex items-center text-gray-400 hover:text-blue-400" onclick="communityFeed.toggleComments(${post.id}, this)">
                        <i class="far fa-comment mr-1"></i>
                        <span>${post.comments_count}</span>
                    </button>
                    <button class="reaction-btn flex items-center text-gray-400 hover:text-green-400">
                        <i class="far fa-share-square mr-1"></i>
                        <span>Share</span>
                    </button>
                </div>
            </div>

            <div class="comment-section mt-4 pt-4 border-t border-gray-800" id="comments-${post.id}">
                <!-- Comments will be loaded here -->
            </div>
        `;

        return postDiv;
    }

    // Interaction methods
    async toggleLike(type, id, button) {
        try {
            console.log('Toggling like:', { type, id });

            const response = await fetch('/community/toggleLike', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `type=${type}&id=${id}`
            });

            console.log('Response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Response data:', data);

            if (data.success) {
                const icon = button.querySelector('i');
                const countSpan = button.querySelector('.like-count');

                if (data.liked) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    button.classList.add('text-teal-400');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    button.classList.remove('text-teal-400');
                }

                countSpan.textContent = data.like_count;
            } else {
                console.error('Server error:', data.message);
                this.showError(data.message || 'Failed to toggle like');
            }
        } catch (error) {
            console.error('Error toggling like:', error);
            this.showError('Failed to toggle like: ' + error.message);
        }
    }

    async toggleFollow(userId, button) {
        try {
            const response = await fetch('/community/toggleFollow', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `user_id=${userId}`
            });

            const data = await response.json();

            if (data.success) {
                if (data.following) {
                    button.textContent = 'Following';
                    button.classList.add('following', 'bg-gray-700', 'border', 'border-teal-400');
                    button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                } else {
                    button.textContent = 'Follow';
                    button.classList.remove('following', 'bg-gray-700', 'border', 'border-teal-400');
                    button.classList.add('bg-blue-600', 'hover:bg-blue-700');
                }
            } else {
                this.showError(data.message || 'Failed to toggle follow');
            }
        } catch (error) {
            console.error('Error toggling follow:', error);
            this.showError('Failed to toggle follow');
        }
    }

    async toggleComments(postId, button) {
        const commentsSection = document.getElementById(`comments-${postId}`);

        if (commentsSection.classList.contains('active')) {
            commentsSection.classList.remove('active');
        } else {
            commentsSection.classList.add('active');
            await this.loadComments(postId);
        }
    }

    async loadComments(postId) {
        try {
            const response = await fetch(`/community/getComments/${postId}`);
            const data = await response.json();

            if (data.success) {
                const container = document.getElementById(`comments-${postId}`);
                container.innerHTML = this.renderComments(data.comments, postId);
            } else {
                this.showError(data.message || 'Failed to load comments');
            }
        } catch (error) {
            console.error('Error loading comments:', error);
            this.showError('Failed to load comments');
        }
    }

    renderComments(comments, postId) {
        let html = '<div class="space-y-4 mb-4">';

        comments.forEach(comment => {
            html += this.createCommentHTML(comment);
        });

        html += '</div>';

        // Add comment form
        html += `
            <div class="flex items-start space-x-3">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-8 h-8 rounded-full">
                <div class="flex-1 flex comment-input-container">
                    <input type="text" placeholder="Write a comment..." class="comment-input flex-1 rounded-l-lg px-4 py-2 text-white focus:outline-none" onkeypress="if(event.key==='Enter') communityFeed.addComment(${postId}, this)">
                    <button class="bg-teal-600 hover:bg-teal-500 text-white px-4 rounded-r-lg" onclick="communityFeed.addComment(${postId}, this.previousElementSibling)">Post</button>
                </div>
            </div>
        `;

        return html;
    }

    createCommentHTML(comment) {
        let html = `
            <div class="flex">
                <img src="${comment.profile || 'https://randomuser.me/api/portraits/men/32.jpg'}" alt="User" class="w-8 h-8 rounded-full mr-3">
                <div class="flex-1">
                    <div class="flex items-center">
                        <div class="text-sm font-medium text-white">${comment.full_name}</div>
                        <span class="text-xs text-gray-500 ml-2">${comment.time_ago}</span>
                    </div>
                    <p class="text-sm text-gray-300">${comment.content}</p>
                    <div class="flex text-xs text-gray-500 mt-1">
                        <button class="hover:text-teal-400 reply-btn" onclick="communityFeed.toggleReply(${comment.id})">Reply</button>
                    </div>

                    <div class="reply-section" id="reply-${comment.id}">
                        <div class="flex mt-3">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-6 h-6 rounded-full mr-2">
                            <div class="text-xs text-gray-400">Replying to <span class="text-teal-400">${comment.full_name}</span></div>
                        </div>
                        <div class="flex items-start space-x-2 mt-2">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-6 h-6 rounded-full">
                            <div class="flex-1 flex comment-input-container">
                                <input type="text" placeholder="Write your reply..." class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm" onkeypress="if(event.key==='Enter') communityFeed.addReply(${comment.post_id}, ${comment.id}, this)">
                                <button class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm" onclick="communityFeed.addReply(${comment.post_id}, ${comment.id}, this.previousElementSibling)">Reply</button>
                            </div>
                        </div>
                    </div>
        `;

        // Add nested replies
        if (comment.replies && comment.replies.length > 0) {
            comment.replies.forEach(reply => {
                html += `
                    <div class="mt-3 pl-4 border-l-2 border-gray-700">
                        <div class="flex">
                            <img src="${reply.profile || 'https://randomuser.me/api/portraits/men/32.jpg'}" alt="User" class="w-8 h-8 rounded-full mr-3">
                            <div>
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-white">${reply.full_name}</div>
                                    <span class="reply-indicator">↳ replying to ${comment.full_name}</span>
                                    <span class="text-xs text-gray-500 ml-2">${reply.time_ago}</span>
                                </div>
                                <p class="text-sm text-gray-300">${reply.content}</p>
                            </div>
                        </div>
                    </div>
                `;
            });
        }

        html += '</div></div>';
        return html;
    }

    async addComment(postId, input) {
        const content = input.value.trim();

        if (!content) return;

        try {
            const response = await fetch('/community/addComment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `post_id=${postId}&content=${encodeURIComponent(content)}`
            });

            const data = await response.json();

            if (data.success) {
                input.value = '';
                await this.loadComments(postId);
                this.showSuccess('Comment added successfully!');
            } else {
                this.showError(data.message || 'Failed to add comment');
            }
        } catch (error) {
            console.error('Error adding comment:', error);
            this.showError('Failed to add comment');
        }
    }

    async addReply(postId, parentId, input) {
        const content = input.value.trim();

        if (!content) return;

        try {
            const response = await fetch('/community/addComment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `post_id=${postId}&content=${encodeURIComponent(content)}&parent_id=${parentId}`
            });

            const data = await response.json();

            if (data.success) {
                input.value = '';
                this.toggleReply(parentId);
                await this.loadComments(postId);
                this.showSuccess('Reply added successfully!');
            } else {
                this.showError(data.message || 'Failed to add reply');
            }
        } catch (error) {
            console.error('Error adding reply:', error);
            this.showError('Failed to add reply');
        }
    }

    toggleReply(commentId) {
        const replySection = document.getElementById(`reply-${commentId}`);
        replySection.classList.toggle('active');
    }

    async deletePost(postId) {
        // Create custom confirmation dialog
        const confirmed = await this.showConfirmDialog(
            'Delete Post',
            'Are you sure you want to delete this post? This action cannot be undone.',
            'Delete',
            'Cancel'
        );

        if (!confirmed) {
            return;
        }

        try {
            console.log('Deleting post:', postId);

            const response = await fetch(`/community/deletePost/${postId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: '_method=DELETE'
            });

            console.log('Delete response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Delete response data:', data);

            if (data.success) {
                const postElement = document.querySelector(`[data-post-id="${postId}"]`);
                if (postElement) {
                    postElement.remove();
                    this.showSuccess('Post deleted successfully!');
                    console.log('Post element removed from DOM');
                } else {
                    console.warn('Post element not found in DOM');
                    this.showSuccess('Post deleted successfully!');
                }
            } else {
                console.error('Server error:', data.message);
                this.showError(data.message || 'Failed to delete post');
            }
        } catch (error) {
            console.error('Error deleting post:', error);
            this.showError('Failed to delete post: ' + error.message);
        }
    }

    // Utility functions
    getBadgeClass(badge) {
        const classes = {
            'pro': 'badge-pro',
            'gainer': 'badge-gainer',
            'master': 'badge-master',
            'analyst': 'badge-pro'
        };
        return classes[badge] || 'bg-gray-600';
    }

    getPostTypeColor(postType) {
        const colors = {
            'setup': 'text-purple-400',
            'pnl': 'text-green-400',
            'analysis': 'text-blue-400',
            'educational': 'text-yellow-400',
            'general': 'text-gray-400'
        };
        return colors[postType] || 'text-gray-400';
    }

    formatPostType(postType) {
        const labels = {
            'setup': 'Setup Alert',
            'pnl': 'P&L Share',
            'analysis': 'Analysis',
            'educational': 'Educational',
            'general': 'General'
        };
        return labels[postType] || 'Post';
    }

    formatBadge(badge) {
        const labels = {
            'pro': 'Pro Analyst',
            'gainer': 'Consistent Gainer',
            'master': 'Chart Master',
            'analyst': 'Pro Analyst'
        };
        return labels[badge] || badge;
    }

    getTagClass(type) {
        const classes = {
            'hashtag': 'hashtag',
            'mention': 'mention',
            'stock': 'stock-tag'
        };
        return classes[type] || 'text-gray-400';
    }

    formatTag(tag) {
        const prefixes = {
            'hashtag': '#',
            'mention': '@',
            'stock': '$'
        };
        return (prefixes[tag.type] || '') + tag.value;
    }

    formatContent(content) {
        // Simple content formatting - replace hashtags, mentions, and stock symbols
        return content
            .replace(/#(\w+)/g, '<span class="hashtag">#$1</span>')
            .replace(/@(\w+)/g, '<span class="mention">@$1</span>')
            .replace(/\$(\w+)/g, '<span class="stock-tag">$$$1</span>');
    }

    showSuccess(message) {
        // You can implement a toast notification here
        console.log('Success:', message);
    }

    showError(message) {
        // You can implement a toast notification here
        console.error('Error:', message);
    }

    showConfirmDialog(title, message, confirmText = 'Confirm', cancelText = 'Cancel') {
        return new Promise((resolve) => {
            // Create modal HTML
            const modalHTML = `
                <div id="confirmModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                        <h3 class="text-lg font-semibold text-white mb-4">${title}</h3>
                        <p class="text-gray-300 mb-6">${message}</p>
                        <div class="flex justify-end space-x-3">
                            <button id="confirmCancel" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition">
                                ${cancelText}
                            </button>
                            <button id="confirmDelete" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition">
                                ${confirmText}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to DOM
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            const modal = document.getElementById('confirmModal');
            const cancelBtn = document.getElementById('confirmCancel');
            const confirmBtn = document.getElementById('confirmDelete');

            // Handle cancel
            const handleCancel = () => {
                modal.remove();
                resolve(false);
            };

            // Handle confirm
            const handleConfirm = () => {
                modal.remove();
                resolve(true);
            };

            // Add event listeners
            cancelBtn.addEventListener('click', handleCancel);
            confirmBtn.addEventListener('click', handleConfirm);
            modal.addEventListener('click', (e) => {
                if (e.target === modal) handleCancel();
            });

            // Focus on cancel button by default
            cancelBtn.focus();
        });
    }
}

// Initialize community feed when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Prevent multiple initializations
    if (window.communityFeed) {
        return;
    }
    window.communityFeed = new CommunityFeed();
});
