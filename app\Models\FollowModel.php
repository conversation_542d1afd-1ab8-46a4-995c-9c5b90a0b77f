<?php

namespace App\Models;

use CodeIgniter\Model;

class FollowModel extends Model
{
    protected $table = 'community_follows';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'follower_id',
        'following_id'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = false;
    protected $deletedField = false;

    // Validation
    protected $validationRules = [
        'follower_id' => 'required|integer',
        'following_id' => 'required|integer|differs[follower_id]'
    ];

    protected $validationMessages = [
        'follower_id' => [
            'required' => 'Follower ID is required',
            'integer' => 'Follower ID must be an integer'
        ],
        'following_id' => [
            'required' => 'Following ID is required',
            'integer' => 'Following ID must be an integer',
            'differs' => 'Users cannot follow themselves'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Toggle follow relationship
     */
    public function toggleFollow($followerId, $followingId)
    {
        // Prevent self-following
        if ($followerId == $followingId) {
            return ['action' => 'error', 'message' => 'Cannot follow yourself', 'following' => false];
        }

        $existingFollow = $this->where([
            'follower_id' => $followerId,
            'following_id' => $followingId
        ])->first();

        if ($existingFollow) {
            // Unfollow - remove the follow relationship
            $this->delete($existingFollow['id']);
            $this->decrementFollowCounts($followerId, $followingId);
            return ['action' => 'unfollowed', 'following' => false];
        } else {
            // Follow - add the follow relationship
            $data = [
                'follower_id' => $followerId,
                'following_id' => $followingId
            ];
            
            if ($this->insert($data)) {
                $this->incrementFollowCounts($followerId, $followingId);
                return ['action' => 'followed', 'following' => true];
            }
            return ['action' => 'error', 'message' => 'Failed to follow user', 'following' => false];
        }
    }

    /**
     * Check if user is following another user
     */
    public function isFollowing($followerId, $followingId)
    {
        return $this->where([
            'follower_id' => $followerId,
            'following_id' => $followingId
        ])->first() !== null;
    }

    /**
     * Get followers of a user
     */
    public function getFollowers($userId, $limit = 50, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' f');
        $builder->select('u.id, u.full_name, u.profile, u.badge, u.followers_count, u.posts_count, f.created_at as followed_at');
        $builder->join('users u', 'u.id = f.follower_id');
        $builder->where('f.following_id', $userId);
        $builder->orderBy('f.created_at', 'DESC');
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get users that a user is following
     */
    public function getFollowing($userId, $limit = 50, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' f');
        $builder->select('u.id, u.full_name, u.profile, u.badge, u.followers_count, u.posts_count, f.created_at as followed_at');
        $builder->join('users u', 'u.id = f.following_id');
        $builder->where('f.follower_id', $userId);
        $builder->orderBy('f.created_at', 'DESC');
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get follower count for a user
     */
    public function getFollowerCount($userId)
    {
        return $this->where('following_id', $userId)->countAllResults();
    }

    /**
     * Get following count for a user
     */
    public function getFollowingCount($userId)
    {
        return $this->where('follower_id', $userId)->countAllResults();
    }

    /**
     * Get follow status for multiple users
     */
    public function getFollowStatusForUsers($followerId, $userIds)
    {
        if (empty($userIds)) {
            return [];
        }

        $builder = $this->db->table($this->table);
        $builder->select('following_id');
        $builder->where('follower_id', $followerId);
        $builder->whereIn('following_id', $userIds);
        
        $follows = $builder->get()->getResultArray();
        
        $followingUsers = [];
        foreach ($follows as $follow) {
            $followingUsers[$follow['following_id']] = true;
        }
        
        return $followingUsers;
    }

    /**
     * Get posts from users that the current user follows
     */
    public function getFollowingPosts($userId, $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' f');
        $builder->select('p.*, u.full_name, u.profile, u.badge');
        $builder->join('community_posts p', 'p.user_id = f.following_id');
        $builder->join('users u', 'u.id = p.user_id');
        $builder->where('f.follower_id', $userId);
        $builder->where('p.deleted_at', null);
        $builder->orderBy('p.created_at', 'DESC');
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get suggested users to follow (users with most followers that current user doesn't follow)
     */
    public function getSuggestedUsers($userId, $limit = 10)
    {
        $builder = $this->db->table('users u');
        $builder->select('u.id, u.full_name, u.profile, u.badge, u.followers_count, u.posts_count');
        $builder->where('u.id !=', $userId);
        $builder->where('u.deleted_at', null);
        
        // Exclude users already being followed
        $subQuery = $this->db->table($this->table)
                            ->select('following_id')
                            ->where('follower_id', $userId);
        $builder->whereNotIn('u.id', $subQuery);
        
        $builder->orderBy('u.followers_count', 'DESC');
        $builder->orderBy('u.posts_count', 'DESC');
        $builder->limit($limit);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Remove all follows for a user (for cleanup)
     */
    public function removeUserFollows($userId)
    {
        // Remove where user is follower
        $this->where('follower_id', $userId)->delete();
        
        // Remove where user is being followed
        $this->where('following_id', $userId)->delete();
        
        return true;
    }

    /**
     * Increment follow counts in users table
     */
    private function incrementFollowCounts($followerId, $followingId)
    {
        $userModel = new UserModel();
        
        // Increment following count for follower
        $userModel->set('following_count', 'following_count + 1', false)
                 ->where('id', $followerId)
                 ->update();
        
        // Increment followers count for the user being followed
        $userModel->set('followers_count', 'followers_count + 1', false)
                 ->where('id', $followingId)
                 ->update();
    }

    /**
     * Decrement follow counts in users table
     */
    private function decrementFollowCounts($followerId, $followingId)
    {
        $userModel = new UserModel();
        
        // Decrement following count for follower
        $userModel->set('following_count', 'following_count - 1', false)
                 ->where('id', $followerId)
                 ->where('following_count >', 0)
                 ->update();
        
        // Decrement followers count for the user being followed
        $userModel->set('followers_count', 'followers_count - 1', false)
                 ->where('id', $followingId)
                 ->where('followers_count >', 0)
                 ->update();
    }
}
