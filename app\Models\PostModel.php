<?php

namespace App\Models;

use CodeIgniter\Model;

class PostModel extends Model
{
    protected $table = 'community_posts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'user_id',
        'title',
        'content',
        'post_type',
        'asset_class',
        'tags',
        'image_url',
        'likes_count',
        'comments_count',
        'shares_count',
        'is_pinned'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'title' => 'required|max_length[255]',
        'content' => 'required',
        'post_type' => 'required|in_list[setup,pnl,analysis,educational,general]',
        'asset_class' => 'required|in_list[equity,options,futures,crypto,forex,all]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'title' => [
            'required' => 'Post title is required',
            'max_length' => 'Title cannot exceed 255 characters'
        ],
        'content' => [
            'required' => 'Post content is required'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = ['updateUserPostsCount'];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = ['decrementUserPostsCount'];

    /**
     * Get posts with user information and interaction counts
     */
    public function getPostsWithUserInfo($limit = 20, $offset = 0, $filters = [])
    {
        $builder = $this->db->table($this->table . ' p');
        $builder->select('p.*, u.full_name, u.profile, u.badge');
        $builder->join('users u', 'u.id = p.user_id');
        $builder->where('p.deleted_at', null);
        
        // Apply filters
        if (!empty($filters['post_type']) && $filters['post_type'] !== 'all') {
            $builder->where('p.post_type', $filters['post_type']);
        }
        
        if (!empty($filters['asset_class']) && $filters['asset_class'] !== 'all') {
            $builder->where('p.asset_class', $filters['asset_class']);
        }
        
        if (!empty($filters['user_id'])) {
            $builder->where('p.user_id', $filters['user_id']);
        }

        // Apply sorting
        $sortBy = $filters['sort'] ?? 'latest';
        switch ($sortBy) {
            case 'trending':
                $builder->orderBy('(p.likes_count + p.comments_count * 2)', 'DESC');
                $builder->orderBy('p.created_at', 'DESC');
                break;
            case 'most_liked':
                $builder->orderBy('p.likes_count', 'DESC');
                break;
            case 'latest':
            default:
                $builder->orderBy('p.is_pinned', 'DESC');
                $builder->orderBy('p.created_at', 'DESC');
                break;
        }
        
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get user's posts
     */
    public function getUserPosts($userId, $limit = 20, $offset = 0)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Increment likes count
     */
    public function incrementLikes($postId)
    {
        return $this->set('likes_count', 'likes_count + 1', false)
                   ->where('id', $postId)
                   ->update();
    }

    /**
     * Decrement likes count
     */
    public function decrementLikes($postId)
    {
        return $this->set('likes_count', 'likes_count - 1', false)
                   ->where('id', $postId)
                   ->where('likes_count >', 0)
                   ->update();
    }

    /**
     * Increment comments count
     */
    public function incrementComments($postId)
    {
        return $this->set('comments_count', 'comments_count + 1', false)
                   ->where('id', $postId)
                   ->update();
    }

    /**
     * Decrement comments count
     */
    public function decrementComments($postId)
    {
        return $this->set('comments_count', 'comments_count - 1', false)
                   ->where('id', $postId)
                   ->where('comments_count >', 0)
                   ->update();
    }

    /**
     * Search posts by content or tags
     */
    public function searchPosts($query, $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' p');
        $builder->select('p.*, u.full_name, u.profile, u.badge');
        $builder->join('users u', 'u.id = p.user_id');
        $builder->where('p.deleted_at', null);
        
        $builder->groupStart();
        $builder->like('p.title', $query);
        $builder->orLike('p.content', $query);
        $builder->orLike('p.tags', $query);
        $builder->groupEnd();
        
        $builder->orderBy('p.created_at', 'DESC');
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Update user posts count after insert
     */
    protected function updateUserPostsCount(array $data)
    {
        if (isset($data['data']['user_id'])) {
            $userModel = new UserModel();
            $userModel->set('posts_count', 'posts_count + 1', false)
                     ->where('id', $data['data']['user_id'])
                     ->update();
        }
        return $data;
    }

    /**
     * Decrement user posts count after delete
     */
    protected function decrementUserPostsCount(array $data)
    {
        if (isset($data['id'])) {
            $post = $this->withDeleted()->find($data['id']);
            if ($post) {
                $userModel = new UserModel();
                $userModel->set('posts_count', 'posts_count - 1', false)
                         ->where('id', $post['user_id'])
                         ->where('posts_count >', 0)
                         ->update();
            }
        }
        return $data;
    }
}
