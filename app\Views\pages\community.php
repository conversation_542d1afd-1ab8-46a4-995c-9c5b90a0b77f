<!-- Community Feed Styles -->
<style>
    :root {
        --primary: #4fd1c5;
        --primary-dark: #319795;
        --secondary: #805ad5;
        --secondary-dark: #6b46c1;
        --accent: #f687b3;
        --accent-dark: #e53e3e;
        --dark: #1a202c;
        --darker: #171923;
        --light: #f7fafc;
        --gray: #e2e8f0;
        --dark-gray: #2d3748;
    }

    .glass-card {
        background: rgba(26, 32, 44, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.08);
    }

    .post-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(79, 209, 197, 0.1);
    }

    .glow-button {
        box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 15px rgba(79, 209, 197, 0.5); }
        50% { box-shadow: 0 0 25px rgba(79, 209, 197, 0.8); }
        100% { box-shadow: 0 0 15px rgba(79, 209, 197, 0.5); }
    }

    .hashtag { color: var(--primary); }
    .mention { color: var(--accent); }
    .stock-tag { color: var(--secondary); }

    .code-block {
        background-color: #2d3748;
        border-radius: 6px;
        padding: 12px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        overflow-x: auto;
    }

    .reaction-btn:hover { transform: scale(1.1); }
    .badge-pro { background: linear-gradient(90deg, var(--primary), var(--secondary)); }
    .badge-gainer { background: linear-gradient(90deg, #48bb78, #38b2ac); }
    .badge-master { background: linear-gradient(90deg, var(--accent), #ed8936); }
    .tab-active { border-bottom: 2px solid var(--primary); color: var(--primary); }

    .comment-section {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
    }

    .comment-section.active {
        max-height: 1000px;
        transition: max-height 0.5s ease-in;
    }

    .comment-input {
        background: rgba(45, 55, 72, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .comment-input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .neon-text { text-shadow: 0 0 5px rgba(79, 209, 197, 0.5); }

    .modal {
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
    }

    .modal.active {
        opacity: 1;
        visibility: visible;
    }

    .modal-content {
        transform: translateY(-20px);
        transition: transform 0.3s ease;
    }

    .modal.active .modal-content { transform: translateY(0); }

    .post-type-btn {
        transition: all 0.2s ease;
    }

    .post-type-btn.active {
        background-color: var(--primary);
        color: white;
    }

    .post-type-btn:hover:not(.active) {
        background-color: rgba(79, 209, 197, 0.1);
    }

    .follow-btn {
        background: linear-gradient(90deg, var(--primary), var(--secondary));
        transition: all 0.3s ease;
    }

    .follow-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(79, 209, 197, 0.3);
    }

    .follow-btn.following {
        background: var(--dark-gray);
        border: 1px solid var(--primary);
    }

    .reply-section {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        margin-left: 2rem;
        border-left: 2px solid var(--primary);
        padding-left: 1rem;
    }

    .reply-section.active { max-height: 200px; }
    .reply-btn { color: var(--primary); font-size: 0.8rem; }
    .reply-btn:hover { text-decoration: underline; }
    .reply-indicator { color: var(--primary); font-size: 0.7rem; margin-left: 0.5rem; }

    .sidebar {
        transition: all 0.3s ease;
        background-color: var(--dark);
    }

    .sidebar-collapsed {
        width: 80px !important;
    }

    .sidebar-collapsed .nav-text {
        display: none;
    }

    .sidebar-collapsed .logo-text {
        display: none;
    }

    .sidebar-collapsed .logo-icon {
        margin: 0 auto;
    }

    .sidebar-collapsed + .main-content {
        margin-left: 80px;
    }

    .nav-item.active {
        background-color: var(--dark-gray) !important;
        color: white !important;
    }

    .nav-item:hover {
        background-color: var(--dark-gray);
        color: white;
    }

    @media (max-width: 1024px) {
        .sidebar {
            left: -256px;
        }

        .sidebar.active {
            left: 0;
        }

        .main-content {
            margin-left: 0 !important;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 40;
            display: none;
        }

        .overlay.active {
            display: block;
        }
    }
        .feed-header { flex-direction: column; align-items: flex-start; }
        .feed-header h1 { margin-bottom: 1rem; }
        .feed-filters { width: 100%; flex-direction: column; gap: 0.5rem; }
        .feed-filters select { width: 100%; }
        .tabs { overflow-x: auto; white-space: nowrap; padding-bottom: 0.5rem; -webkit-overflow-scrolling: touch; }
        .tabs::-webkit-scrollbar { display: none; }
        .post-actions { flex-wrap: wrap; gap: 0.5rem; }
        .post-actions > div { flex: 1 1 100%; justify-content: space-between; }
        .user-info { flex-direction: column; align-items: flex-start; }
        .user-badges { margin-top: 0.5rem; width: 100%; justify-content: space-between; }
        .modal-content { width: 95%; margin: 0 auto; }
        .comment-input-container { flex-direction: column; }
        .comment-input-container input { border-radius: 0.5rem 0.5rem 0 0 !important; width: 100%; }
        .comment-input-container button { border-radius: 0 0 0.5rem 0.5rem !important; width: 100%; }
        .reply-section { margin-left: 1rem; }
    }
</style>

<!-- Community Sidebar -->
<div class="sidebar fixed left-0 top-0 h-full w-64 bg-gray-900 border-r border-gray-800 flex flex-col z-50 transition-all duration-300 ease-in-out">
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-800">
        <div class="flex items-center space-x-2">
            <div class="logo-icon">
                <i class="fas fa-book text-2xl text-teal-400"></i>
            </div>
            <span class="logo-text text-xl font-bold text-teal-400 neon-text">TRADE DIARY</span>
        </div>
        <button id="sidebarToggle" class="text-gray-400 hover:text-white focus:outline-none lg:block hidden">
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>

    <!-- Sidebar Navigation -->
    <div class="flex-1 overflow-y-auto py-4">
        <nav class="px-2">
            <ul class="space-y-1">
                <li>
                    <a href="<?= base_url('community') ?>" class="nav-item active flex items-center px-3 py-3 rounded-lg text-white bg-gray-800 transition-colors duration-200">
                        <i class="fas fa-home text-teal-400 w-5 h-5 mr-3"></i>
                        <span class="nav-text">Home Feed</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200" onclick="communityFeed.switchTab('my-posts'); return false;">
                        <i class="fas fa-pen text-blue-400 w-5 h-5 mr-3"></i>
                        <span class="nav-text">My Posts</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200" onclick="communityFeed.switchTab('setup'); return false;">
                        <i class="fas fa-lightbulb text-purple-400 w-5 h-5 mr-3"></i>
                        <span class="nav-text">Setups & Ideas</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200" onclick="communityFeed.switchTab('pnl'); return false;">
                        <i class="fas fa-chart-line text-green-400 w-5 h-5 mr-3"></i>
                        <span class="nav-text">P&L Shares</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200" onclick="communityFeed.switchTab('analysis'); return false;">
                        <i class="fas fa-search-dollar text-yellow-400 w-5 h-5 mr-3"></i>
                        <span class="nav-text">Market Analysis</span>
                    </a>
                </li>
            </ul>

            <!-- Divider -->
            <div class="border-t border-gray-800 my-4"></div>

            <ul class="space-y-1">
                <li>
                    <a href="<?= base_url('dashboard') ?>" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200">
                        <i class="fas fa-tachometer-alt text-blue-400 w-5 h-5 mr-3"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="<?= base_url('myTrades') ?>" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200">
                        <i class="fas fa-exchange-alt text-orange-400 w-5 h-5 mr-3"></i>
                        <span class="nav-text">Trades</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200">
                        <i class="fas fa-bell text-indigo-400 w-5 h-5 mr-3"></i>
                        <span class="nav-text">Notifications</span>
                        <span class="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">3</span>
                    </a>
                </li>
            </ul>

            <!-- Bottom Section -->
            <div class="border-t border-gray-800 mt-4 pt-4">
                <ul class="space-y-1">
                    <li>
                        <a href="<?= base_url('MyProfile') ?>" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200">
                            <i class="fas fa-user text-cyan-400 w-5 h-5 mr-3"></i>
                            <span class="nav-text">Profile</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="nav-item flex items-center px-3 py-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200">
                            <i class="fas fa-cog text-gray-400 w-5 h-5 mr-3"></i>
                            <span class="nav-text">Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </div>

    <!-- User Profile Section -->
    <div class="p-4 border-t border-gray-800 bg-gray-800">
        <div class="flex items-center">
            <img src="<?= $userDetails['profile'] ?? 'https://randomuser.me/api/portraits/men/32.jpg' ?>" alt="User" class="w-10 h-10 rounded-full border-2 border-teal-400">
            <div class="ml-3 flex-1">
                <div class="text-sm font-medium text-white truncate"><?= $userDetails['full_name'] ?? 'User' ?></div>
                <div class="text-xs text-gray-400"><?= ucfirst($userDetails['badge'] ?? 'member') ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Overlay for mobile -->
<div class="overlay fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

<!-- Main Community Content -->
<div class="main-content ml-64 min-h-screen bg-gray-900 transition-all duration-300 ease-in-out">
    <!-- Mobile header -->
    <div class="lg:hidden flex items-center justify-between p-4 border-b border-gray-800 bg-gray-900">
        <button id="mobileSidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
            <i class="fas fa-bars text-xl"></i>
        </button>
        <div class="text-xl font-bold text-teal-400 neon-text">TRADE DIARY</div>
        <div class="w-6"></div>
    </div>

    <!-- Main feed -->
    <div class="p-4">
        <!-- Feed header with filters -->
        <div class="flex items-center justify-between mb-6 feed-header">
            <h1 class="text-2xl font-bold text-white neon-text">COMMUNITY FEED</h1>
            <div class="flex space-x-2 feed-filters">
                <div class="relative">
                    <select id="assetFilter" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                        <option value="all">All Assets</option>
                        <option value="equity">Equity</option>
                        <option value="options">Options</option>
                        <option value="futures">Futures</option>
                        <option value="crypto">Crypto</option>
                        <option value="forex">Forex</option>
                    </select>
                </div>
                <div class="relative">
                    <select id="sortFilter" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                        <option value="latest">Latest</option>
                        <option value="trending">Trending</option>
                        <option value="most_liked">Most Liked</option>
                    </select>
                </div>
                <button id="createPostBtn" class="glow-button bg-teal-600 hover:bg-teal-500 text-white px-4 py-2 rounded-lg font-medium">
                    <i class="fas fa-plus mr-2"></i>New Post
                </button>
            </div>
        </div>

        <!-- Tabs -->
        <div class="flex border-b border-gray-800 mb-6 tabs">
            <button class="tab-btn tab-active px-4 py-2 font-medium text-white" data-tab="all">All</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="setup">Setups</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="pnl">P&L</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="analysis">Analysis</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="educational">Educational</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="my-posts">My Posts</button>
        </div>

        <!-- Posts Container -->
        <div class="space-y-6" id="postsContainer">
            <!-- Posts will be loaded here via AJAX -->
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-400 mx-auto"></div>
                <p class="text-gray-400 mt-4">Loading posts...</p>
            </div>
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-8">
            <button id="loadMoreBtn" class="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium" style="display: none;">
                Load More Posts
            </button>
        </div>
    </div>
</div>

<!-- Create Post Modal -->
<div class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="createPostModal">
    <div class="modal-content glass-card rounded-xl p-6 w-full max-w-2xl mx-4">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-white">Create New Post</h2>
            <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="createPostForm" enctype="multipart/form-data">
            <!-- Post Type Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Post Type</label>
                <div class="flex flex-wrap gap-2 post-type-buttons">
                    <button type="button" class="post-type-btn active bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="general">
                        <i class="fas fa-comment mr-2"></i>General
                    </button>
                    <button type="button" class="post-type-btn bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="setup">
                        <i class="fas fa-chart-line mr-2"></i>Setup
                    </button>
                    <button type="button" class="post-type-btn bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="pnl">
                        <i class="fas fa-dollar-sign mr-2"></i>P&L Share
                    </button>
                    <button type="button" class="post-type-btn bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="analysis">
                        <i class="fas fa-search mr-2"></i>Analysis
                    </button>
                    <button type="button" class="post-type-btn bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="educational">
                        <i class="fas fa-graduation-cap mr-2"></i>Educational
                    </button>
                </div>
                <input type="hidden" name="post_type" id="postType" value="general">
            </div>

            <!-- Asset Class Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Asset Class</label>
                <select name="asset_class" id="assetClass" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2">
                    <option value="all">All Assets</option>
                    <option value="equity">Equity</option>
                    <option value="options">Options</option>
                    <option value="futures">Futures</option>
                    <option value="crypto">Crypto</option>
                    <option value="forex">Forex</option>
                </select>
            </div>

            <!-- Title -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                <input type="text" name="title" id="postTitle" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2" placeholder="Enter post title..." required>
            </div>

            <!-- Content -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Content</label>
                <textarea name="content" id="postContent" rows="6" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2" placeholder="Share your thoughts, analysis, or trading insights... Use #hashtags, @mentions, and $symbols" required></textarea>
                <p class="text-xs text-gray-500 mt-1">Tip: Use #hashtags for topics, @mentions for users, and $symbols for stocks</p>
            </div>

            <!-- Image Upload -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Attach Image/Chart (Optional)</label>
                <div class="flex items-center justify-center w-full">
                    <label for="postImage" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-700">
                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                            <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                            <p class="mb-2 text-sm text-gray-400">
                                <span class="font-semibold">Click to upload</span> or drag and drop
                            </p>
                            <p class="text-xs text-gray-500">PNG, JPG or GIF (MAX. 5MB)</p>
                        </div>
                        <input id="postImage" name="image" type="file" class="hidden" accept="image/*">
                    </label>
                </div>
                <div id="imagePreview" class="mt-4 hidden">
                    <img id="previewImg" src="" alt="Preview" class="max-w-full h-48 object-cover rounded-lg">
                    <button type="button" id="removeImage" class="mt-2 text-red-400 hover:text-red-300 text-sm">
                        <i class="fas fa-trash mr-1"></i>Remove Image
                    </button>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-3">
                <button type="button" id="cancelPostBtn" class="bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium">
                    Cancel
                </button>
                <button type="submit" id="submitPostBtn" class="glow-button bg-teal-600 hover:bg-teal-500 text-white px-6 py-2 rounded-lg font-medium">
                    <i class="fas fa-paper-plane mr-2"></i>Post
                </button>
            </div>
        </form>
    </div>
</div>
