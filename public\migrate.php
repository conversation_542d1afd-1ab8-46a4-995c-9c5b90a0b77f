<?php
/**
 * Migration Runner Script for XAMPP
 * 
 * This script runs the CodeIgniter 4 migrations through a web browser
 * Place this file in your public folder and access it via browser
 * 
 * URL: http://localhost/your-project/migrate.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define paths
define('ROOTPATH', realpath(dirname(__FILE__) . '/../') . DIRECTORY_SEPARATOR);
define('APPPATH', ROOTPATH . 'app' . DIRECTORY_SEPARATOR);
define('SYSTEMPATH', ROOTPATH . 'vendor/codeigniter4/framework/system/');
define('FCPATH', dirname(__FILE__) . DIRECTORY_SEPARATOR);

// Load CodeIgniter
require_once ROOTPATH . 'vendor/autoload.php';

// Bootstrap CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Community Migration Runner</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Community Migration Runner</h1>
        
        <?php
        $action = $_GET['action'] ?? 'status';
        
        try {
            // Get migration service
            $migrate = \Config\Services::migrations();
            
            switch ($action) {
                case 'run':
                    echo '<h2>Running Migrations...</h2>';
                    
                    try {
                        $result = $migrate->latest();
                        
                        if ($result === true) {
                            echo '<div class="success">✅ <strong>Success!</strong> All migrations have been executed successfully.</div>';
                            echo '<div class="info">The community database tables have been created and are ready to use!</div>';
                        } else {
                            echo '<div class="error">❌ <strong>Error:</strong> Migration failed or no migrations to run.</div>';
                        }
                        
                    } catch (Exception $e) {
                        echo '<div class="error">❌ <strong>Migration Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
                        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
                    }
                    
                    break;
                    
                case 'rollback':
                    echo '<h2>Rolling Back Last Migration...</h2>';
                    
                    try {
                        $result = $migrate->regress();
                        
                        if ($result === true) {
                            echo '<div class="success">✅ <strong>Success!</strong> Last migration has been rolled back.</div>';
                        } else {
                            echo '<div class="error">❌ <strong>Error:</strong> Rollback failed or no migrations to rollback.</div>';
                        }
                        
                    } catch (Exception $e) {
                        echo '<div class="error">❌ <strong>Rollback Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
                    }
                    
                    break;
                    
                case 'status':
                default:
                    echo '<h2>Migration Status</h2>';
                    
                    try {
                        // Check current migration version
                        $db = \Config\Database::connect();
                        
                        // Check if migrations table exists
                        if ($db->tableExists('migrations')) {
                            $query = $db->query("SELECT * FROM migrations ORDER BY version DESC LIMIT 10");
                            $migrations = $query->getResultArray();
                            
                            if (!empty($migrations)) {
                                echo '<div class="info">📊 <strong>Recent Migrations:</strong></div>';
                                echo '<pre>';
                                foreach ($migrations as $migration) {
                                    echo "Version: {$migration['version']} | Class: {$migration['class']} | Group: {$migration['group']} | Namespace: {$migration['namespace']}\n";
                                }
                                echo '</pre>';
                            } else {
                                echo '<div class="info">📝 No migrations have been run yet.</div>';
                            }
                        } else {
                            echo '<div class="info">📝 Migrations table does not exist. No migrations have been run yet.</div>';
                        }
                        
                        // Check if community tables exist
                        $communityTables = [
                            'community_posts',
                            'community_comments', 
                            'community_likes',
                            'community_follows'
                        ];
                        
                        echo '<h3>Community Tables Status:</h3>';
                        foreach ($communityTables as $table) {
                            if ($db->tableExists($table)) {
                                $count = $db->table($table)->countAll();
                                echo '<div class="success">✅ Table <strong>' . $table . '</strong> exists (Records: ' . $count . ')</div>';
                            } else {
                                echo '<div class="error">❌ Table <strong>' . $table . '</strong> does not exist</div>';
                            }
                        }
                        
                        // Check if user table has community fields
                        if ($db->tableExists('users')) {
                            $fields = $db->getFieldNames('users');
                            $communityFields = ['followers_count', 'following_count', 'posts_count', 'bio', 'badge'];
                            
                            echo '<h3>User Table Community Fields:</h3>';
                            foreach ($communityFields as $field) {
                                if (in_array($field, $fields)) {
                                    echo '<div class="success">✅ Field <strong>' . $field . '</strong> exists in users table</div>';
                                } else {
                                    echo '<div class="error">❌ Field <strong>' . $field . '</strong> missing from users table</div>';
                                }
                            }
                        }
                        
                    } catch (Exception $e) {
                        echo '<div class="error">❌ <strong>Database Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
                    }
                    
                    break;
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ <strong>System Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
            echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
        }
        ?>
        
        <h2>Actions</h2>
        <div>
            <a href="?action=status" class="btn">📊 Check Status</a>
            <a href="?action=run" class="btn btn-success" onclick="return confirm('Are you sure you want to run migrations? This will create/modify database tables.')">🚀 Run Migrations</a>
            <a href="?action=rollback" class="btn btn-danger" onclick="return confirm('Are you sure you want to rollback the last migration? This may delete data.')">↩️ Rollback Last</a>
        </div>
        
        <div class="info">
            <h3>📋 Instructions:</h3>
            <ol>
                <li><strong>Check Status:</strong> View current migration status and table information</li>
                <li><strong>Run Migrations:</strong> Execute all pending migrations to create community tables</li>
                <li><strong>Rollback:</strong> Undo the last migration (use with caution)</li>
            </ol>
            
            <h3>🎯 What the Community Migration Creates:</h3>
            <ul>
                <li><strong>community_posts</strong> - Main posts table with titles, content, types, and metadata</li>
                <li><strong>community_comments</strong> - Comments and replies with nested structure</li>
                <li><strong>community_likes</strong> - Like system for posts and comments</li>
                <li><strong>community_follows</strong> - User following relationships</li>
                <li><strong>users table updates</strong> - Adds community fields (followers_count, posts_count, etc.)</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666;">
            <small>Community Migration Runner v1.0 | Trade Diary Platform</small>
        </div>
    </div>
</body>
</html>
